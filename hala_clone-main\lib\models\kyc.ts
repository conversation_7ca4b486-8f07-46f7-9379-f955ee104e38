export type KYCVerificationType = 'individual' | 'business'
export type KYCStatus = 'pending' | 'approved' | 'rejected' | 'incomplete'
export type DocumentCategory = 'identity' | 'address' | 'business' | 'representative'

export interface KYCVerification {
  id?: string
  user_id: string
  verification_type: KYCVerificationType
  status: KYCStatus
  created_at: string
  updated_at: string
  submitted_at?: string
  reviewed_at?: string
  reviewer_notes?: string
  
  // Individual KYC fields
  first_name?: string
  last_name?: string
  email?: string
  phone?: string
  date_of_birth?: string
  address?: string
  city?: string
  postal_code?: string
  country?: string
  
  // Business KYC fields
  company_name?: string
  company_type?: string
  company_registration_number?: string
  company_tax_id?: string
  founding_date?: string
  company_address?: string
  company_city?: string
  company_postal_code?: string
  company_country?: string
  
  // Legal representative fields (for business)
  representative_first_name?: string
  representative_last_name?: string
  representative_email?: string
  representative_phone?: string
  representative_date_of_birth?: string
  representative_position?: string
  representative_address?: string
  representative_city?: string
  representative_postal_code?: string
  representative_country?: string
}

export interface KYCDocument {
  id?: string
  kyc_verification_id: string
  document_type: string
  document_category: DocumentCategory
  file_path: string
  file_name: string
  file_size?: number
  mime_type?: string
  uploaded_at: string
  is_verified: boolean
  verification_notes?: string
}

// Form data interfaces for frontend
export interface IndividualKYCFormData {
  first_name: string
  last_name: string
  email: string
  phone: string
  date_of_birth: string
  address: string
  city: string
  postal_code: string
  country: string
}

export interface BusinessKYCFormData {
  company_name: string
  company_type: string
  company_registration_number: string
  company_tax_id: string
  founding_date: string
  company_address: string
  company_city: string
  company_postal_code: string
  company_country: string
}

export interface RepresentativeKYCFormData {
  representative_first_name: string
  representative_last_name: string
  representative_email: string
  representative_phone: string
  representative_date_of_birth: string
  representative_position: string
  representative_address: string
  representative_city: string
  representative_postal_code: string
  representative_country: string
}

export interface KYCSubmissionData {
  verification_type: KYCVerificationType
  individual_data?: IndividualKYCFormData
  business_data?: BusinessKYCFormData
  representative_data?: RepresentativeKYCFormData
  documents: Array<{
    document_type: string
    document_category: DocumentCategory
    file: File
  }>
}

// Document type mappings
export const INDIVIDUAL_DOCUMENT_TYPES = {
  passport: 'Passport',
  id_card: 'ID Card',
  drivers_license: "Driver's License",
  selfie: 'Selfie with Document'
} as const

export const BUSINESS_DOCUMENT_TYPES = {
  certificate_of_incorporation: 'Certificate of Incorporation',
  tax_certificate: 'Tax Certificate',
  bank_statement: 'Bank Statement',
  utility_bill: 'Utility Bill',
  representative_id: 'Representative ID Document',
  representative_selfie: 'Representative Selfie'
} as const

export const DOCUMENT_CATEGORIES = {
  identity: 'Identity Documents',
  address: 'Address Verification',
  business: 'Business Documents',
  representative: 'Representative Documents'
} as const

// Country options
export const COUNTRIES = [
  { value: 'it', label: 'Italy' },
  { value: 'fr', label: 'France' },
  { value: 'de', label: 'Germany' },
  { value: 'es', label: 'Spain' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'au', label: 'Australia' },
  { value: 'nl', label: 'Netherlands' },
  { value: 'be', label: 'Belgium' },
  { value: 'ch', label: 'Switzerland' },
  { value: 'at', label: 'Austria' },
  { value: 'pt', label: 'Portugal' },
  { value: 'ie', label: 'Ireland' },
  { value: 'se', label: 'Sweden' },
  { value: 'no', label: 'Norway' },
  { value: 'dk', label: 'Denmark' },
  { value: 'fi', label: 'Finland' }
] as const

// Company types
export const COMPANY_TYPES = [
  { value: 'llc', label: 'LLC' },
  { value: 'corporation', label: 'Corporation' },
  { value: 'partnership', label: 'Partnership' },
  { value: 'sole_proprietorship', label: 'Sole Proprietorship' },
  { value: 'srl', label: 'SRL (Italy)' },
  { value: 'spa', label: 'SpA (Italy)' },
  { value: 'sarl', label: 'SARL (France)' },
  { value: 'sa', label: 'SA (France)' },
  { value: 'gmbh', label: 'GmbH (Germany)' },
  { value: 'ag', label: 'AG (Germany)' },
  { value: 'sl', label: 'SL (Spain)' },
  { value: 'other', label: 'Other' }
] as const
