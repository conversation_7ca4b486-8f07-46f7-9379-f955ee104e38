import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseAdminClient, corsHeaders, errorResponse, successResponse, getUserFromRequest } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get authenticated user
    const user = await getUserFromRequest(req)
    if (!user) {
      return errorResponse('Unauthorized', 401)
    }

    if (req.method !== 'POST') {
      return errorResponse('Method not allowed', 405)
    }

    const formData = await req.formData()
    const file = formData.get('file') as File
    const documentType = formData.get('documentType') as string
    const documentCategory = formData.get('documentCategory') as string
    const kycVerificationId = formData.get('kycVerificationId') as string

    if (!file || !documentType || !documentCategory || !kycVerificationId) {
      return errorResponse('Missing required fields', 400)
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      return errorResponse('Invalid file type. Only JPEG, PNG, and PDF files are allowed.', 400)
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return errorResponse('File size too large. Maximum size is 10MB.', 400)
    }

    const supabase = createSupabaseAdminClient()

    // Verify that the KYC verification belongs to the user
    const { data: kycVerification, error: kycError } = await supabase
      .from('kyc_verifications')
      .select('id, user_id')
      .eq('id', kycVerificationId)
      .eq('user_id', user.id)
      .single()

    if (kycError || !kycVerification) {
      return errorResponse('KYC verification not found or access denied', 404)
    }

    // Generate filename
    const timestamp = Date.now()
    const extension = file.name.split('.').pop()
    const fileName = `${user.id}/${kycVerificationId}/${documentType}_${timestamp}.${extension}`

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('kyc-documents')
      .upload(fileName, file, {
        contentType: file.type,
        upsert: false,
      })

    if (uploadError) {
      console.error('Upload error:', uploadError)
      return errorResponse('Upload failed', 500)
    }

    // Check if document already exists for this type
    const { data: existingDoc } = await supabase
      .from('kyc_documents')
      .select('id')
      .eq('kyc_verification_id', kycVerificationId)
      .eq('document_type', documentType)
      .single()

    if (existingDoc) {
      // Update existing document
      const { data: updatedDoc, error: updateError } = await supabase
        .from('kyc_documents')
        .update({
          file_path: uploadData.path,
          file_name: file.name,
          file_size: file.size,
          mime_type: file.type,
          uploaded_at: new Date().toISOString(),
          is_verified: false,
          verification_notes: null
        })
        .eq('id', existingDoc.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating document metadata:', updateError)
        return errorResponse('Failed to update document metadata', 500)
      }

      return successResponse({
        document: updatedDoc,
        path: uploadData.path,
        message: 'Document updated successfully'
      })
    } else {
      // Create new document record
      const { data: newDoc, error: insertError } = await supabase
        .from('kyc_documents')
        .insert({
          kyc_verification_id: kycVerificationId,
          document_type: documentType,
          document_category: documentCategory,
          file_path: uploadData.path,
          file_name: file.name,
          file_size: file.size,
          mime_type: file.type,
        })
        .select()
        .single()

      if (insertError) {
        console.error('Error saving document metadata:', insertError)
        return errorResponse('Failed to save document metadata', 500)
      }

      return successResponse({
        document: newDoc,
        path: uploadData.path,
        message: 'Document uploaded successfully'
      })
    }

  } catch (error) {
    console.error('Document upload error:', error)
    return errorResponse('Internal server error', 500)
  }
})
