<!DOCTYPE html>
<html>
<head>
    <title>Test Upload</title>
</head>
<body>
    <h1>Test Image Upload</h1>
    <input type="file" id="fileInput" accept="image/*">
    <button onclick="testUpload()">Test Upload</button>
    <div id="result"></div>

    <script>
        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                document.getElementById('result').innerHTML = 'Please select a file';
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('https://dcdslxzhypxpledhkvtw.supabase.co/functions/v1/upload-image', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8'
                    },
                    body: formData
                });

                const result = await response.json();
                document.getElementById('result').innerHTML = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
