import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseAdminClient, corsHeaders, errorResponse, successResponse, getUserFromRequest } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get authenticated user
    const user = await getUserFromRequest(req)
    if (!user) {
      return errorResponse('Unauthorized', 401)
    }

    if (req.method !== 'POST') {
      return errorResponse('Method not allowed', 405)
    }

    const formData = await req.formData()
    const kycDataStr = formData.get('kycData') as string
    
    if (!kycDataStr) {
      return errorResponse('Missing KYC data', 400)
    }

    const kycData = JSON.parse(kycDataStr)
    const { verification_type, individual_data, business_data, representative_data } = kycData

    if (!verification_type || !['individual', 'business'].includes(verification_type)) {
      return errorResponse('Invalid verification type', 400)
    }

    const supabase = createSupabaseAdminClient()

    // Check if user already has a KYC verification
    const { data: existingKyc } = await supabase
      .from('kyc_verifications')
      .select('id, status')
      .eq('user_id', user.id)
      .single()

    let kycVerificationId: string

    if (existingKyc) {
      // Update existing KYC verification
      const updateData: any = {
        verification_type,
        status: 'pending',
        submitted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // Add individual data if provided
      if (individual_data) {
        Object.assign(updateData, individual_data)
      }

      // Add business data if provided
      if (business_data) {
        Object.assign(updateData, business_data)
      }

      // Add representative data if provided
      if (representative_data) {
        Object.assign(updateData, representative_data)
      }

      const { error: updateError } = await supabase
        .from('kyc_verifications')
        .update(updateData)
        .eq('id', existingKyc.id)

      if (updateError) {
        console.error('Error updating KYC verification:', updateError)
        return errorResponse('Failed to update KYC verification', 500)
      }

      kycVerificationId = existingKyc.id
    } else {
      // Create new KYC verification
      const insertData: any = {
        user_id: user.id,
        verification_type,
        status: 'pending',
        submitted_at: new Date().toISOString()
      }

      // Add individual data if provided
      if (individual_data) {
        Object.assign(insertData, individual_data)
      }

      // Add business data if provided
      if (business_data) {
        Object.assign(insertData, business_data)
      }

      // Add representative data if provided
      if (representative_data) {
        Object.assign(insertData, representative_data)
      }

      const { data: newKyc, error: insertError } = await supabase
        .from('kyc_verifications')
        .insert(insertData)
        .select('id')
        .single()

      if (insertError || !newKyc) {
        console.error('Error creating KYC verification:', insertError)
        return errorResponse('Failed to create KYC verification', 500)
      }

      kycVerificationId = newKyc.id
    }

    // Handle document uploads
    const uploadedDocuments = []
    const documentFiles = []

    // Collect all document files from form data
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('document_') && value instanceof File) {
        const documentType = key.replace('document_', '')
        documentFiles.push({ type: documentType, file: value })
      }
    }

    // Upload documents to storage and save metadata
    for (const { type, file } of documentFiles) {
      try {
        // Generate filename
        const timestamp = Date.now()
        const extension = file.name.split('.').pop()
        const fileName = `${user.id}/${kycVerificationId}/${type}_${timestamp}.${extension}`

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('kyc-documents')
          .upload(fileName, file, {
            contentType: file.type,
            upsert: false,
          })

        if (uploadError) {
          console.error('Upload error:', uploadError)
          continue // Skip this document but continue with others
        }

        // Determine document category based on type
        let documentCategory = 'identity'
        if (type.includes('company') || type.includes('certificate') || type.includes('tax')) {
          documentCategory = 'business'
        } else if (type.includes('representative')) {
          documentCategory = 'representative'
        } else if (type.includes('address') || type.includes('utility')) {
          documentCategory = 'address'
        }

        // Save document metadata to database
        const { data: docData, error: docError } = await supabase
          .from('kyc_documents')
          .insert({
            kyc_verification_id: kycVerificationId,
            document_type: type,
            document_category: documentCategory,
            file_path: uploadData.path,
            file_name: file.name,
            file_size: file.size,
            mime_type: file.type,
          })
          .select('id')
          .single()

        if (docError) {
          console.error('Error saving document metadata:', docError)
          continue
        }

        uploadedDocuments.push({
          id: docData.id,
          type,
          fileName: file.name,
          path: uploadData.path
        })
      } catch (error) {
        console.error('Error processing document:', error)
        continue
      }
    }

    return successResponse({
      kyc_verification_id: kycVerificationId,
      status: 'pending',
      uploaded_documents: uploadedDocuments,
      message: 'KYC verification submitted successfully'
    })

  } catch (error) {
    console.error('KYC submission error:', error)
    return errorResponse('Internal server error', 500)
  }
})
