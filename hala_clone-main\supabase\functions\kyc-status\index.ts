import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseAdminClient, corsHeaders, errorResponse, successResponse, getUserFromRequest } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get authenticated user
    const user = await getUserFromRequest(req)
    if (!user) {
      return errorResponse('Unauthorized', 401)
    }

    if (req.method !== 'GET') {
      return errorResponse('Method not allowed', 405)
    }

    const supabase = createSupabaseAdminClient()

    // Get user's KYC verification
    const { data: kycVerification, error: kycError } = await supabase
      .from('kyc_verifications')
      .select(`
        *,
        kyc_documents (
          id,
          document_type,
          document_category,
          file_name,
          uploaded_at,
          is_verified,
          verification_notes
        )
      `)
      .eq('user_id', user.id)
      .single()

    if (kycError && kycError.code !== 'PGRST116') {
      console.error('Error fetching KYC verification:', kycError)
      return errorResponse('Failed to fetch KYC status', 500)
    }

    if (!kycVerification) {
      return successResponse({
        status: 'not_started',
        verification: null,
        documents: [],
        message: 'No KYC verification found'
      })
    }

    // Format the response
    const response = {
      status: kycVerification.status,
      verification: {
        id: kycVerification.id,
        verification_type: kycVerification.verification_type,
        status: kycVerification.status,
        created_at: kycVerification.created_at,
        updated_at: kycVerification.updated_at,
        submitted_at: kycVerification.submitted_at,
        reviewed_at: kycVerification.reviewed_at,
        reviewer_notes: kycVerification.reviewer_notes,
        
        // Individual data
        first_name: kycVerification.first_name,
        last_name: kycVerification.last_name,
        email: kycVerification.email,
        phone: kycVerification.phone,
        date_of_birth: kycVerification.date_of_birth,
        address: kycVerification.address,
        city: kycVerification.city,
        postal_code: kycVerification.postal_code,
        country: kycVerification.country,
        
        // Business data
        company_name: kycVerification.company_name,
        company_type: kycVerification.company_type,
        company_registration_number: kycVerification.company_registration_number,
        company_tax_id: kycVerification.company_tax_id,
        founding_date: kycVerification.founding_date,
        company_address: kycVerification.company_address,
        company_city: kycVerification.company_city,
        company_postal_code: kycVerification.company_postal_code,
        company_country: kycVerification.company_country,
        
        // Representative data
        representative_first_name: kycVerification.representative_first_name,
        representative_last_name: kycVerification.representative_last_name,
        representative_email: kycVerification.representative_email,
        representative_phone: kycVerification.representative_phone,
        representative_date_of_birth: kycVerification.representative_date_of_birth,
        representative_position: kycVerification.representative_position,
        representative_address: kycVerification.representative_address,
        representative_city: kycVerification.representative_city,
        representative_postal_code: kycVerification.representative_postal_code,
        representative_country: kycVerification.representative_country,
      },
      documents: kycVerification.kyc_documents || [],
      message: 'KYC status retrieved successfully'
    }

    return successResponse(response)

  } catch (error) {
    console.error('KYC status error:', error)
    return errorResponse('Internal server error', 500)
  }
})
