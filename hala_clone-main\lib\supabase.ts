import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Singleton client instance to avoid multiple instances
let supabaseInstance: ReturnType<typeof createClient> | null = null

// Client-side Supabase client (safe for browser use)
export const createBrowserClient = () => {
  if (!supabaseInstance) {
    supabaseInstance = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      }
    })
  }
  return supabaseInstance
}

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'customer' | 'business'
          created_at: string
          updated_at: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          role: 'customer' | 'business'
          created_at?: string
          updated_at?: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          role?: 'customer' | 'business'
          created_at?: string
          updated_at?: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
      }
      items: {
        Row: {
          id: string
          name: string
          description?: string
          image_url: string
          brand?: string
          year?: string
          serial_number?: string
          created_at: string
          creator_id: string
          owner_id: string
          is_active: boolean
        }
        Insert: {
          id?: string
          name: string
          description?: string
          image_url: string
          brand?: string
          year?: string
          serial_number?: string
          created_at?: string
          creator_id: string
          owner_id: string
          is_active?: boolean
        }
        Update: {
          id?: string
          name?: string
          description?: string
          image_url?: string
          brand?: string
          year?: string
          serial_number?: string
          created_at?: string
          creator_id?: string
          owner_id?: string
          is_active?: boolean
        }
      }
      ownership_history: {
        Row: {
          id: string
          item_id: string
          user_id: string
          user_name: string
          transferred_at: string
          transferred_from?: string
          transferred_from_name?: string
        }
        Insert: {
          id?: string
          item_id: string
          user_id: string
          user_name: string
          transferred_at?: string
          transferred_from?: string
          transferred_from_name?: string
        }
        Update: {
          id?: string
          item_id?: string
          user_id?: string
          user_name?: string
          transferred_at?: string
          transferred_from?: string
          transferred_from_name?: string
        }
      }
      transfers: {
        Row: {
          id: string
          item_id: string
          item_name: string
          sender_id: string
          sender_name: string
          sender_email: string
          recipient_id: string
          recipient_email: string
          message?: string
          transferred_at: string
          status: string
        }
        Insert: {
          id?: string
          item_id: string
          item_name: string
          sender_id: string
          sender_name: string
          sender_email: string
          recipient_id: string
          recipient_email: string
          message?: string
          transferred_at?: string
          status?: string
        }
        Update: {
          id?: string
          item_id?: string
          item_name?: string
          sender_id?: string
          sender_name?: string
          sender_email?: string
          recipient_id?: string
          recipient_email?: string
          message?: string
          transferred_at?: string
          status?: string
        }
      }
      kyc_verifications: {
        Row: {
          id: string
          user_id: string
          verification_type: 'individual' | 'business'
          status: 'pending' | 'approved' | 'rejected' | 'incomplete'
          created_at: string
          updated_at: string
          submitted_at?: string
          reviewed_at?: string
          reviewer_notes?: string
          first_name?: string
          last_name?: string
          email?: string
          phone?: string
          date_of_birth?: string
          address?: string
          city?: string
          postal_code?: string
          country?: string
          company_name?: string
          company_type?: string
          company_registration_number?: string
          company_tax_id?: string
          founding_date?: string
          company_address?: string
          company_city?: string
          company_postal_code?: string
          company_country?: string
          representative_first_name?: string
          representative_last_name?: string
          representative_email?: string
          representative_phone?: string
          representative_date_of_birth?: string
          representative_position?: string
          representative_address?: string
          representative_city?: string
          representative_postal_code?: string
          representative_country?: string
        }
        Insert: {
          id?: string
          user_id: string
          verification_type: 'individual' | 'business'
          status?: 'pending' | 'approved' | 'rejected' | 'incomplete'
          created_at?: string
          updated_at?: string
          submitted_at?: string
          reviewed_at?: string
          reviewer_notes?: string
          first_name?: string
          last_name?: string
          email?: string
          phone?: string
          date_of_birth?: string
          address?: string
          city?: string
          postal_code?: string
          country?: string
          company_name?: string
          company_type?: string
          company_registration_number?: string
          company_tax_id?: string
          founding_date?: string
          company_address?: string
          company_city?: string
          company_postal_code?: string
          company_country?: string
          representative_first_name?: string
          representative_last_name?: string
          representative_email?: string
          representative_phone?: string
          representative_date_of_birth?: string
          representative_position?: string
          representative_address?: string
          representative_city?: string
          representative_postal_code?: string
          representative_country?: string
        }
        Update: {
          id?: string
          user_id?: string
          verification_type?: 'individual' | 'business'
          status?: 'pending' | 'approved' | 'rejected' | 'incomplete'
          created_at?: string
          updated_at?: string
          submitted_at?: string
          reviewed_at?: string
          reviewer_notes?: string
          first_name?: string
          last_name?: string
          email?: string
          phone?: string
          date_of_birth?: string
          address?: string
          city?: string
          postal_code?: string
          country?: string
          company_name?: string
          company_type?: string
          company_registration_number?: string
          company_tax_id?: string
          founding_date?: string
          company_address?: string
          company_city?: string
          company_postal_code?: string
          company_country?: string
          representative_first_name?: string
          representative_last_name?: string
          representative_email?: string
          representative_phone?: string
          representative_date_of_birth?: string
          representative_position?: string
          representative_address?: string
          representative_city?: string
          representative_postal_code?: string
          representative_country?: string
        }
      }
      kyc_documents: {
        Row: {
          id: string
          kyc_verification_id: string
          document_type: string
          document_category: 'identity' | 'address' | 'business' | 'representative'
          file_path: string
          file_name: string
          file_size?: number
          mime_type?: string
          uploaded_at: string
          is_verified: boolean
          verification_notes?: string
        }
        Insert: {
          id?: string
          kyc_verification_id: string
          document_type: string
          document_category: 'identity' | 'address' | 'business' | 'representative'
          file_path: string
          file_name: string
          file_size?: number
          mime_type?: string
          uploaded_at?: string
          is_verified?: boolean
          verification_notes?: string
        }
        Update: {
          id?: string
          kyc_verification_id?: string
          document_type?: string
          document_category?: 'identity' | 'address' | 'business' | 'representative'
          file_path?: string
          file_name?: string
          file_size?: number
          mime_type?: string
          uploaded_at?: string
          is_verified?: boolean
          verification_notes?: string
        }
      }
    }
  }
}
