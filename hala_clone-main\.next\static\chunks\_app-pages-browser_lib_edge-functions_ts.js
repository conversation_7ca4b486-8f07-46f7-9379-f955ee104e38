"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_edge-functions_ts"],{

/***/ "(app-pages-browser)/./lib/edge-functions.ts":
/*!*******************************!*\
  !*** ./lib/edge-functions.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsAPI: () => (/* binding */ analyticsAPI),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   edgeFunctions: () => (/* binding */ edgeFunctions),\n/* harmony export */   kycAPI: () => (/* binding */ kycAPI),\n/* harmony export */   nftAPI: () => (/* binding */ nftAPI),\n/* harmony export */   uploadAPI: () => (/* binding */ uploadAPI)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n\n// Edge Functions client\nclass EdgeFunctionsClient {\n    async callFunction(functionName) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const { method = 'POST', body, headers = {} } = options;\n        // Get the current session\n        const { data: { session } } = await this.supabase.auth.getSession();\n        if (session === null || session === void 0 ? void 0 : session.access_token) {\n            headers['Authorization'] = \"Bearer \".concat(session.access_token);\n        }\n        const requestOptions = {\n            method,\n            headers: {\n                'Content-Type': 'application/json',\n                ...headers\n            }\n        };\n        if (body && method !== 'GET') {\n            if (body instanceof FormData) {\n                // Remove Content-Type for FormData to let browser set it with boundary\n                delete requestOptions.headers['Content-Type'];\n                requestOptions.body = body;\n            } else {\n                requestOptions.body = JSON.stringify(body);\n            }\n        }\n        const response = await fetch(\"\".concat(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"/functions/v1/\").concat(functionName), requestOptions);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    error: 'Unknown error'\n                }));\n            // Log detailed error information for debugging\n            console.error('Edge Function Error:', {\n                functionName,\n                status: response.status,\n                statusText: response.statusText,\n                errorData,\n                url: response.url\n            });\n            // Handle authentication errors specifically\n            if (response.status === 401 || response.status === 403) {\n                // Redirect to login if not authenticated\n                if (true) {\n                    window.location.href = \"/auth/login?redirectTo=\".concat(encodeURIComponent(window.location.pathname));\n                }\n                throw new Error('Authentication required');\n            }\n            throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n        }\n        return response.json();\n    }\n    // Authentication functions\n    async login(email, password) {\n        return this.callFunction('auth-login', {\n            body: {\n                email,\n                password\n            }\n        });\n    }\n    async signup(data) {\n        return this.callFunction('auth-signup', {\n            body: data\n        });\n    }\n    async logout() {\n        return this.callFunction('auth-logout');\n    }\n    // Item functions\n    async mintItem(data) {\n        return this.callFunction('item-mint', {\n            body: data\n        });\n    }\n    async getMyItems() {\n        return this.callFunction('item-list', {\n            method: 'GET'\n        });\n    }\n    async transferItem(data) {\n        return this.callFunction('item-transfer', {\n            body: data\n        });\n    }\n    // Upload functions\n    async uploadImage(file, itemId) {\n        const formData = new FormData();\n        formData.append('file', file);\n        if (itemId) {\n            formData.append('itemId', itemId);\n        }\n        return this.callFunction('upload-image', {\n            body: formData\n        });\n    }\n    async deleteImage(path) {\n        return this.callFunction('upload-image', {\n            method: 'DELETE',\n            body: {\n                path\n            }\n        });\n    }\n    // Analytics functions\n    async getAnalytics() {\n        let timeRange = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '30d';\n        return this.callFunction(\"dashboard-analytics?timeRange=\".concat(timeRange), {\n            method: 'GET'\n        });\n    }\n    // KYC Methods\n    async submitKYC(kycData, documents) {\n        const formData = new FormData();\n        formData.append('kycData', JSON.stringify(kycData));\n        // Add document files\n        documents.forEach((param)=>{\n            let { type, file } = param;\n            formData.append(\"document_\".concat(type), file);\n        });\n        return this.callFunction('kyc-submit', {\n            body: formData\n        });\n    }\n    async getKYCStatus() {\n        return this.callFunction('kyc-status', {\n            method: 'GET'\n        });\n    }\n    async uploadKYCDocument(file, documentType, documentCategory, kycVerificationId) {\n        const formData = new FormData();\n        formData.append('file', file);\n        formData.append('documentType', documentType);\n        formData.append('documentCategory', documentCategory);\n        formData.append('kycVerificationId', kycVerificationId);\n        return this.callFunction('kyc-upload-document', {\n            body: formData\n        });\n    }\n    constructor(){\n        this.supabase = (0,_supabase__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)();\n    }\n}\nconst edgeFunctions = new EdgeFunctionsClient();\n// Backward compatibility - these functions can be used to gradually migrate from Next.js API routes\nconst authAPI = {\n    login: edgeFunctions.login.bind(edgeFunctions),\n    signup: edgeFunctions.signup.bind(edgeFunctions),\n    logout: edgeFunctions.logout.bind(edgeFunctions)\n};\nconst nftAPI = {\n    mint: edgeFunctions.mintItem.bind(edgeFunctions),\n    getMyNfts: edgeFunctions.getMyItems.bind(edgeFunctions),\n    transfer: edgeFunctions.transferItem.bind(edgeFunctions)\n};\nconst uploadAPI = {\n    uploadImage: edgeFunctions.uploadImage.bind(edgeFunctions),\n    deleteImage: edgeFunctions.deleteImage.bind(edgeFunctions)\n};\nconst analyticsAPI = {\n    getAnalytics: edgeFunctions.getAnalytics.bind(edgeFunctions)\n};\nconst kycAPI = {\n    submitKYC: edgeFunctions.submitKYC.bind(edgeFunctions),\n    getKYCStatus: edgeFunctions.getKYCStatus.bind(edgeFunctions),\n    uploadKYCDocument: edgeFunctions.uploadKYCDocument.bind(edgeFunctions)\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/edge-functions.ts\n"));

/***/ })

}]);