-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends auth.users)
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  full_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  role VARCHAR(20) CHEC<PERSON> (role IN ('customer', 'business')) NOT NULL DEFAULT 'customer',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  business_name VARCHAR(255),
  business_vat_number VARCHAR(100),
  avatar_url TEXT
);

-- Create items table (formerly nfts)
CREATE TABLE items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  brand VARCHAR(255),
  year VARCHAR(10),
  serial_number VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  creator_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  owner_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT TRUE
);

-- Create ownership_history table
CREATE TABLE ownership_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  item_id UUID REFERENCES items(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  user_name VARCHAR(255) NOT NULL,
  transferred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  transferred_from UUID REFERENCES profiles(id) ON DELETE SET NULL,
  transferred_from_name VARCHAR(255)
);

-- Create transfers table
CREATE TABLE transfers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  item_id UUID REFERENCES items(id) ON DELETE CASCADE,
  item_name VARCHAR(255) NOT NULL,
  sender_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  sender_name VARCHAR(255) NOT NULL,
  sender_email VARCHAR(255) NOT NULL,
  recipient_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  recipient_email VARCHAR(255) NOT NULL,
  message TEXT,
  transferred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status VARCHAR(50) DEFAULT 'pending'
);

-- Create KYC verifications table
CREATE TABLE kyc_verifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  verification_type VARCHAR(20) CHECK (verification_type IN ('individual', 'business')) NOT NULL,
  status VARCHAR(20) CHECK (status IN ('pending', 'approved', 'rejected', 'incomplete')) DEFAULT 'incomplete',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  submitted_at TIMESTAMP WITH TIME ZONE,
  reviewed_at TIMESTAMP WITH TIME ZONE,
  reviewer_notes TEXT,

  -- Individual KYC fields
  first_name VARCHAR(255),
  last_name VARCHAR(255),
  email VARCHAR(255),
  phone VARCHAR(50),
  date_of_birth DATE,
  address TEXT,
  city VARCHAR(255),
  postal_code VARCHAR(20),
  country VARCHAR(10),

  -- Business KYC fields (for business users)
  company_name VARCHAR(255),
  company_type VARCHAR(50),
  company_registration_number VARCHAR(100),
  company_tax_id VARCHAR(100),
  founding_date DATE,
  company_address TEXT,
  company_city VARCHAR(255),
  company_postal_code VARCHAR(20),
  company_country VARCHAR(10),

  -- Legal representative fields (for business)
  representative_first_name VARCHAR(255),
  representative_last_name VARCHAR(255),
  representative_email VARCHAR(255),
  representative_phone VARCHAR(50),
  representative_date_of_birth DATE,
  representative_position VARCHAR(100),
  representative_address TEXT,
  representative_city VARCHAR(255),
  representative_postal_code VARCHAR(20),
  representative_country VARCHAR(10)
);

-- Create KYC documents table
CREATE TABLE kyc_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  kyc_verification_id UUID REFERENCES kyc_verifications(id) ON DELETE CASCADE,
  document_type VARCHAR(50) NOT NULL, -- 'passport', 'id_card', 'drivers_license', 'selfie', 'certificate_of_incorporation', 'tax_certificate', etc.
  document_category VARCHAR(20) CHECK (document_category IN ('identity', 'address', 'business', 'representative')) NOT NULL,
  file_path TEXT NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_size INTEGER,
  mime_type VARCHAR(100),
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_verified BOOLEAN DEFAULT FALSE,
  verification_notes TEXT
);

-- Create indexes for better performance
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_items_creator_id ON items(creator_id);
CREATE INDEX idx_items_owner_id ON items(owner_id);
CREATE INDEX idx_items_is_active ON items(is_active);
CREATE INDEX idx_ownership_history_item_id ON ownership_history(item_id);
CREATE INDEX idx_ownership_history_user_id ON ownership_history(user_id);
CREATE INDEX idx_transfers_sender_id ON transfers(sender_id);
CREATE INDEX idx_transfers_recipient_id ON transfers(recipient_id);
CREATE INDEX idx_transfers_item_id ON transfers(item_id);
CREATE INDEX idx_kyc_verifications_user_id ON kyc_verifications(user_id);
CREATE INDEX idx_kyc_verifications_status ON kyc_verifications(status);
CREATE INDEX idx_kyc_verifications_type ON kyc_verifications(verification_type);
CREATE INDEX idx_kyc_documents_verification_id ON kyc_documents(kyc_verification_id);
CREATE INDEX idx_kyc_documents_type ON kyc_documents(document_type);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for profiles table
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for kyc_verifications table
CREATE TRIGGER update_kyc_verifications_updated_at
  BEFORE UPDATE ON kyc_verifications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE items ENABLE ROW LEVEL SECURITY;
ALTER TABLE ownership_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE transfers ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_documents ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Profiles can be read by their owner
CREATE POLICY "Users can read own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

-- Profiles can be updated by their owner
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Profiles can be inserted by their owner (for signup)
CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Items policies
CREATE POLICY "Users can read all items" ON items
  FOR SELECT USING (true);

-- Users can create items
CREATE POLICY "Users can create items" ON items
  FOR INSERT WITH CHECK (auth.uid() = creator_id);

-- Users can update items they own
CREATE POLICY "Users can update own items" ON items
  FOR UPDATE USING (auth.uid() = owner_id);

-- Ownership history policies
CREATE POLICY "Users can read ownership history" ON ownership_history
  FOR SELECT USING (true);

CREATE POLICY "System can insert ownership history" ON ownership_history
  FOR INSERT WITH CHECK (true);

-- Transfers policies
CREATE POLICY "Users can read transfers they're involved in" ON transfers
  FOR SELECT USING (
    auth.uid() = sender_id OR
    auth.uid() = recipient_id
  );

CREATE POLICY "Users can create transfers for items they own" ON transfers
  FOR INSERT WITH CHECK (auth.uid() = sender_id);

-- KYC Verification policies
CREATE POLICY "Users can read their own KYC verification" ON kyc_verifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own KYC verification" ON kyc_verifications
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own KYC verification" ON kyc_verifications
  FOR UPDATE USING (auth.uid() = user_id);

-- KYC Documents policies
CREATE POLICY "Users can read their own KYC documents" ON kyc_documents
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM kyc_verifications
      WHERE kyc_verifications.id = kyc_documents.kyc_verification_id
      AND kyc_verifications.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create their own KYC documents" ON kyc_documents
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM kyc_verifications
      WHERE kyc_verifications.id = kyc_documents.kyc_verification_id
      AND kyc_verifications.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own KYC documents" ON kyc_documents
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM kyc_verifications
      WHERE kyc_verifications.id = kyc_documents.kyc_verification_id
      AND kyc_verifications.user_id = auth.uid()
    )
  );

-- Function to automatically create profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role, business_name, business_vat_number)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    COALESCE(NEW.raw_user_meta_data->>'role', 'customer'),
    NEW.raw_user_meta_data->>'business_name',
    NEW.raw_user_meta_data->>'business_vat_number'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Storage setup for item images
INSERT INTO storage.buckets (id, name, public)
VALUES ('item-images', 'item-images', true);

-- Storage setup for KYC documents (private bucket)
INSERT INTO storage.buckets (id, name, public)
VALUES ('kyc-documents', 'kyc-documents', false);

-- Storage policies for item images
CREATE POLICY "Anyone can view item images" ON storage.objects
  FOR SELECT USING (bucket_id = 'item-images');

CREATE POLICY "Authenticated users can upload item images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'item-images'
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "Users can update their own item images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'item-images'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own item images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'item-images'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for KYC documents
CREATE POLICY "Users can view their own KYC documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'kyc-documents'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Authenticated users can upload KYC documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'kyc-documents'
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "Users can update their own KYC documents" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'kyc-documents'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own KYC documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'kyc-documents'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );
