import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'customer' | 'business'
          created_at: string
          updated_at: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          role: 'customer' | 'business'
          created_at?: string
          updated_at?: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          role?: 'customer' | 'business'
          created_at?: string
          updated_at?: string
          business_name?: string
          business_vat_number?: string
          avatar_url?: string
        }
      }
      items: {
        Row: {
          id: string
          name: string
          description?: string
          image_url: string
          brand?: string
          year?: string
          serial_number?: string
          created_at: string
          creator_id: string
          owner_id: string
          is_active: boolean
        }
        Insert: {
          id?: string
          name: string
          description?: string
          image_url: string
          brand?: string
          year?: string
          serial_number?: string
          created_at?: string
          creator_id: string
          owner_id: string
          is_active?: boolean
        }
        Update: {
          id?: string
          name?: string
          description?: string
          image_url?: string
          brand?: string
          year?: string
          serial_number?: string
          created_at?: string
          creator_id?: string
          owner_id?: string
          is_active?: boolean
        }
      }
      ownership_history: {
        Row: {
          id: string
          item_id: string
          user_id: string
          user_name: string
          transferred_at: string
          transferred_from?: string
          transferred_from_name?: string
        }
        Insert: {
          id?: string
          item_id: string
          user_id: string
          user_name: string
          transferred_at?: string
          transferred_from?: string
          transferred_from_name?: string
        }
        Update: {
          id?: string
          item_id?: string
          user_id?: string
          user_name?: string
          transferred_at?: string
          transferred_from?: string
          transferred_from_name?: string
        }
      }
      transfers: {
        Row: {
          id: string
          item_id: string
          item_name: string
          sender_id: string
          sender_name: string
          sender_email: string
          recipient_id: string
          recipient_email: string
          message?: string
          transferred_at: string
          status: string
        }
        Insert: {
          id?: string
          item_id: string
          item_name: string
          sender_id: string
          sender_name: string
          sender_email: string
          recipient_id: string
          recipient_email: string
          message?: string
          transferred_at?: string
          status?: string
        }
        Update: {
          id?: string
          item_id?: string
          item_name?: string
          sender_id?: string
          sender_name?: string
          sender_email?: string
          recipient_id?: string
          recipient_email?: string
          message?: string
          transferred_at?: string
          status?: string
        }
      }
      kyc_verifications: {
        Row: {
          id: string
          user_id: string
          verification_type: 'individual' | 'business'
          status: 'pending' | 'approved' | 'rejected' | 'incomplete'
          created_at: string
          updated_at: string
          submitted_at?: string
          reviewed_at?: string
          reviewer_notes?: string
          first_name?: string
          last_name?: string
          email?: string
          phone?: string
          date_of_birth?: string
          address?: string
          city?: string
          postal_code?: string
          country?: string
          company_name?: string
          company_type?: string
          company_registration_number?: string
          company_tax_id?: string
          founding_date?: string
          company_address?: string
          company_city?: string
          company_postal_code?: string
          company_country?: string
          representative_first_name?: string
          representative_last_name?: string
          representative_email?: string
          representative_phone?: string
          representative_date_of_birth?: string
          representative_position?: string
          representative_address?: string
          representative_city?: string
          representative_postal_code?: string
          representative_country?: string
        }
        Insert: {
          id?: string
          user_id: string
          verification_type: 'individual' | 'business'
          status?: 'pending' | 'approved' | 'rejected' | 'incomplete'
          created_at?: string
          updated_at?: string
          submitted_at?: string
          reviewed_at?: string
          reviewer_notes?: string
          first_name?: string
          last_name?: string
          email?: string
          phone?: string
          date_of_birth?: string
          address?: string
          city?: string
          postal_code?: string
          country?: string
          company_name?: string
          company_type?: string
          company_registration_number?: string
          company_tax_id?: string
          founding_date?: string
          company_address?: string
          company_city?: string
          company_postal_code?: string
          company_country?: string
          representative_first_name?: string
          representative_last_name?: string
          representative_email?: string
          representative_phone?: string
          representative_date_of_birth?: string
          representative_position?: string
          representative_address?: string
          representative_city?: string
          representative_postal_code?: string
          representative_country?: string
        }
        Update: {
          id?: string
          user_id?: string
          verification_type?: 'individual' | 'business'
          status?: 'pending' | 'approved' | 'rejected' | 'incomplete'
          created_at?: string
          updated_at?: string
          submitted_at?: string
          reviewed_at?: string
          reviewer_notes?: string
          first_name?: string
          last_name?: string
          email?: string
          phone?: string
          date_of_birth?: string
          address?: string
          city?: string
          postal_code?: string
          country?: string
          company_name?: string
          company_type?: string
          company_registration_number?: string
          company_tax_id?: string
          founding_date?: string
          company_address?: string
          company_city?: string
          company_postal_code?: string
          company_country?: string
          representative_first_name?: string
          representative_last_name?: string
          representative_email?: string
          representative_phone?: string
          representative_date_of_birth?: string
          representative_position?: string
          representative_address?: string
          representative_city?: string
          representative_postal_code?: string
          representative_country?: string
        }
      }
      kyc_documents: {
        Row: {
          id: string
          kyc_verification_id: string
          document_type: string
          document_category: 'identity' | 'address' | 'business' | 'representative'
          file_path: string
          file_name: string
          file_size?: number
          mime_type?: string
          uploaded_at: string
          is_verified: boolean
          verification_notes?: string
        }
        Insert: {
          id?: string
          kyc_verification_id: string
          document_type: string
          document_category: 'identity' | 'address' | 'business' | 'representative'
          file_path: string
          file_name: string
          file_size?: number
          mime_type?: string
          uploaded_at?: string
          is_verified?: boolean
          verification_notes?: string
        }
        Update: {
          id?: string
          kyc_verification_id?: string
          document_type?: string
          document_category?: 'identity' | 'address' | 'business' | 'representative'
          file_path?: string
          file_name?: string
          file_size?: number
          mime_type?: string
          uploaded_at?: string
          is_verified?: boolean
          verification_notes?: string
        }
      }
    }
  }
}

// Create Supabase client
export function createSupabaseClient() {
  return createClient<Database>(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? ''
  )
}

// Create admin client with service role
export function createSupabaseAdminClient() {
  return createClient<Database>(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// CORS headers
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

// Error response helper
export function errorResponse(message: string, status = 500) {
  return new Response(
    JSON.stringify({ error: message, success: false }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Success response helper
export function successResponse(data: any, status = 200) {
  return new Response(
    JSON.stringify({ ...data, success: true }),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  )
}

// Get user from request
export async function getUserFromRequest(req: Request) {
  console.log('getUserFromRequest called')

  // Use anon client for auth verification
  const supabaseAuth = createSupabaseClient()

  // Get the authorization header
  const authHeader = req.headers.get('authorization')
  console.log('Authorization header present:', !!authHeader)

  if (!authHeader) {
    console.log('No authorization header found')
    throw new Error('No authorization header')
  }

  // Set the auth header for the client
  const token = authHeader.replace('Bearer ', '')
  console.log('Token extracted, length:', token.length)

  const { data: { user }, error } = await supabaseAuth.auth.getUser(token)
  console.log('Auth getUser result - user:', !!user, 'error:', !!error)

  if (error) {
    console.log('Auth error:', error.message)
    throw new Error('Invalid token')
  }

  if (!user) {
    console.log('No user returned from auth')
    throw new Error('Invalid token')
  }

  console.log('User authenticated, ID:', user.id)

  // Use admin client for profile access (bypasses RLS)
  const supabaseAdmin = createSupabaseAdminClient()

  // Get the user profile using admin client
  const { data: profiles, error: profileError } = await supabaseAdmin
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .limit(1)

  console.log('Profile query result - profiles:', profiles?.length || 0, 'error:', !!profileError)

  if (profileError) {
    console.log('Profile error:', profileError.message)
    throw new Error('Profile not found')
  }

  if (!profiles || profiles.length === 0) {
    console.log('No profiles found for user')
    throw new Error('Profile not found')
  }

  const profile = profiles[0]
  console.log('Profile found, role:', profile.role)

  return { user, profile }
}

// Password strength checker
export function checkPasswordStrength(password: string) {
  const minLength = 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

  const isStrong = password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar

  let feedback = ''
  if (password.length < minLength) feedback += `Password must be at least ${minLength} characters long. `
  if (!hasUpperCase) feedback += 'Password must contain at least one uppercase letter. '
  if (!hasLowerCase) feedback += 'Password must contain at least one lowercase letter. '
  if (!hasNumbers) feedback += 'Password must contain at least one number. '
  if (!hasSpecialChar) feedback += 'Password must contain at least one special character. '

  return { isStrong, feedback: feedback.trim() }
}
